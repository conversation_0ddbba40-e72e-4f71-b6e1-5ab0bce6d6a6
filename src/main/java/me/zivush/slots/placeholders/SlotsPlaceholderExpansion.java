package me.zivush.slots.placeholders;

import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import me.zivush.slots.Slots;
import me.zivush.slots.api.JackpotAPI;
import org.bukkit.OfflinePlayer;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * PlaceholderAPI expansion for the Slots plugin
 * 
 * Provides placeholders for jackpot and slot machine information
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SlotsPlaceholderExpansion extends PlaceholderExpansion {
    
    private final Slots plugin;
    private final JackpotAPI jackpotAPI;
    
    public SlotsPlaceholderExpansion(Slots plugin) {
        this.plugin = plugin;
        this.jackpotAPI = new JackpotAPI(plugin);
    }
    
    @Override
    public @NotNull String getIdentifier() {
        return "slots";
    }
    
    @Override
    public @NotNull String getAuthor() {
        return "Zivush";
    }
    
    @Override
    public @NotNull String getVersion() {
        return plugin.getDescription().getVersion();
    }
    
    @Override
    public boolean persist() {
        return true; // This is required or else PlaceholderAPI will unregister the Expansion on reload
    }
    
    @Override
    public @Nullable String onRequest(OfflinePlayer player, @NotNull String params) {
        
        // %slots_jackpot_pool%
        if (params.equals("jackpot_pool")) {
            return jackpotAPI.getFormattedJackpotPool();
        }
        
        // %slots_jackpot_pool_raw%
        if (params.equals("jackpot_pool_raw")) {
            return String.valueOf(jackpotAPI.getCurrentJackpotPool());
        }
        
        // %slots_jackpot_enabled%
        if (params.equals("jackpot_enabled")) {
            return String.valueOf(jackpotAPI.isJackpotEnabled());
        }
        
        // %slots_jackpot_contribution%
        if (params.equals("jackpot_contribution")) {
            return String.valueOf(jackpotAPI.getJackpotContributionPercent());
        }
        
        // %slots_total_machines%
        if (params.equals("total_machines")) {
            return String.valueOf(jackpotAPI.getTotalSlotMachines());
        }
        
        // %slots_jackpot_display_block%
        if (params.equals("jackpot_display_block")) {
            return jackpotAPI.getJackpotDisplayBlock();
        }
        
        // %slots_jackpot_display_item%
        if (params.equals("jackpot_display_item")) {
            return jackpotAPI.getJackpotDisplayItem();
        }
        
        // %slots_machine_<name>_exists%
        if (params.startsWith("machine_") && params.endsWith("_exists")) {
            String slotName = params.substring(8, params.length() - 7); // Remove "machine_" and "_exists"
            return String.valueOf(jackpotAPI.slotMachineExists(slotName));
        }
        
        // %slots_machine_<name>_jackpot_chance%
        if (params.startsWith("machine_") && params.endsWith("_jackpot_chance")) {
            String slotName = params.substring(8, params.length() - 15); // Remove "machine_" and "_jackpot_chance"
            if (jackpotAPI.slotMachineExists(slotName)) {
                return String.valueOf(jackpotAPI.getJackpotChance(slotName));
            }
            return "0";
        }
        
        // %slots_machine_<name>_win_chance%
        if (params.startsWith("machine_") && params.endsWith("_win_chance")) {
            String slotName = params.substring(8, params.length() - 11); // Remove "machine_" and "_win_chance"
            if (jackpotAPI.slotMachineExists(slotName)) {
                return String.valueOf(plugin.getConfig().getInt("slots." + slotName + ".win_chance", 0));
            }
            return "0";
        }
        
        // %slots_machine_<name>_multiplier%
        if (params.startsWith("machine_") && params.endsWith("_multiplier")) {
            String slotName = params.substring(8, params.length() - 11); // Remove "machine_" and "_multiplier"
            if (jackpotAPI.slotMachineExists(slotName)) {
                return String.valueOf(plugin.getConfig().getDouble("slots." + slotName + ".multiplier", 0.0));
            }
            return "0.0";
        }
        
        // %slots_machine_<name>_min_bet%
        if (params.startsWith("machine_") && params.endsWith("_min_bet")) {
            String slotName = params.substring(8, params.length() - 8); // Remove "machine_" and "_min_bet"
            if (jackpotAPI.slotMachineExists(slotName)) {
                return String.valueOf(plugin.getConfig().getInt("slots." + slotName + ".bet_settings.min_bet", 100));
            }
            return "100";
        }
        
        // %slots_machine_<name>_max_bet%
        if (params.startsWith("machine_") && params.endsWith("_max_bet")) {
            String slotName = params.substring(8, params.length() - 8); // Remove "machine_" and "_max_bet"
            if (jackpotAPI.slotMachineExists(slotName)) {
                return String.valueOf(plugin.getConfig().getInt("slots." + slotName + ".bet_settings.max_bet", 10000));
            }
            return "10000";
        }
        
        // %slots_machine_<name>_default_bet%
        if (params.startsWith("machine_") && params.endsWith("_default_bet")) {
            String slotName = params.substring(8, params.length() - 12); // Remove "machine_" and "_default_bet"
            if (jackpotAPI.slotMachineExists(slotName)) {
                return String.valueOf(plugin.getConfig().getInt("slots." + slotName + ".bet_settings.default_bet", 500));
            }
            return "500";
        }
        
        // We return null if an invalid placeholder (f.e. %slots_somethingnotvalid%) 
        // was provided
        return null;
    }
}
