package me.zivush.slots.api;

import me.zivush.slots.Slots;
import org.bukkit.configuration.file.FileConfiguration;

/**
 * API class for accessing jackpot information from the Slots plugin
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class JackpotAPI {
    
    private final Slots plugin;
    
    public JackpotAPI(Slots plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Gets the current jackpot pool amount
     * 
     * @return The current jackpot pool amount as a double
     */
    public double getCurrentJackpotPool() {
        return plugin.getConfig().getDouble("jackpot.current_pool", 0.0);
    }
    
    /**
     * Gets the current jackpot pool amount formatted as a string
     * 
     * @return The current jackpot pool amount formatted to 2 decimal places
     */
    public String getFormattedJackpotPool() {
        return String.format("%.2f", getCurrentJackpotPool());
    }
    
    /**
     * Checks if the jackpot feature is enabled
     * 
     * @return true if jackpot is enabled, false otherwise
     */
    public boolean isJackpotEnabled() {
        return plugin.getConfig().getBoolean("jackpot.enabled", true);
    }
    
    /**
     * Gets the jackpot contribution percentage
     * 
     * @return The percentage of each bet that goes to the jackpot
     */
    public double getJackpotContributionPercent() {
        return plugin.getConfig().getDouble("jackpot.contribution_percent", 5.0);
    }
    
    /**
     * Gets the jackpot chance for a specific slot machine
     * 
     * @param slotName The name of the slot machine
     * @return The percentage chance to win the jackpot for this slot
     */
    public int getJackpotChance(String slotName) {
        return plugin.getConfig().getInt("slots." + slotName + ".jackpot_chance", 1);
    }
    
    /**
     * Gets the jackpot display block material
     * 
     * @return The material name used for jackpot display blocks
     */
    public String getJackpotDisplayBlock() {
        return plugin.getConfig().getString("jackpot.display.block", "NETHERITE_BLOCK");
    }
    
    /**
     * Gets the jackpot display item material
     * 
     * @return The material name used for jackpot display items
     */
    public String getJackpotDisplayItem() {
        return plugin.getConfig().getString("jackpot.display.item", "NETHERITE_INGOT");
    }
    
    /**
     * Manually adds an amount to the jackpot pool
     * Note: This will save the config automatically
     * 
     * @param amount The amount to add to the jackpot pool
     */
    public void addToJackpotPool(double amount) {
        double currentPool = getCurrentJackpotPool();
        plugin.getConfig().set("jackpot.current_pool", currentPool + amount);
        plugin.saveConfig();
    }
    
    /**
     * Manually sets the jackpot pool amount
     * Note: This will save the config automatically
     * 
     * @param amount The new jackpot pool amount
     */
    public void setJackpotPool(double amount) {
        plugin.getConfig().set("jackpot.current_pool", Math.max(0, amount));
        plugin.saveConfig();
    }
    
    /**
     * Gets the total number of slot machines configured
     * 
     * @return The number of slot machines
     */
    public int getTotalSlotMachines() {
        FileConfiguration config = plugin.getConfig();
        if (config.getConfigurationSection("slots") == null) {
            return 0;
        }
        return config.getConfigurationSection("slots").getKeys(false).size();
    }
    
    /**
     * Checks if a specific slot machine exists
     * 
     * @param slotName The name of the slot machine to check
     * @return true if the slot machine exists, false otherwise
     */
    public boolean slotMachineExists(String slotName) {
        return plugin.getConfig().contains("slots." + slotName);
    }
}
